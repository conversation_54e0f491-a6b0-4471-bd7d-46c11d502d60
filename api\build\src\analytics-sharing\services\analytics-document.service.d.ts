import { Repository } from 'typeorm';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../utils/logger/winston-logger.service';
import { UsersService } from '../../users/users.service';
import { AnalyticsDocumentRequestEntity, AnalyticsDocumentStatus, AnalyticsDocumentType, AnalyticsRecipientType } from '../entities/analytics-document-request.entity';
import { AnalyticsDocumentRequest } from '../interfaces/analytics-sharing.interface';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { SqsService } from '../../utils/aws/sqs/sqs.service';
import { AnalyticsMonitoringService } from './analytics-monitoring.service';
export declare class AnalyticsDocumentService {
    private readonly logger;
    private readonly analyticsDocumentRequestRepository;
    private readonly invoiceRepository;
    private readonly paymentDetailsRepository;
    private readonly patientRepository;
    private readonly ownerBrandRepository;
    private readonly clinicRepository;
    private readonly s3Service;
    private readonly usersService;
    private readonly sqsService;
    private readonly analyticsMonitoringService;
    constructor(logger: WinstonLogger, analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>, invoiceRepository: Repository<InvoiceEntity>, paymentDetailsRepository: Repository<PaymentDetailsEntity>, patientRepository: Repository<Patient>, ownerBrandRepository: Repository<OwnerBrand>, clinicRepository: Repository<ClinicEntity>, s3Service: S3Service, usersService: UsersService, sqsService: SqsService, analyticsMonitoringService: AnalyticsMonitoringService);
    /**
     * Share analytics documents (PDF + Excel) via email
     */
    shareAnalyticsDocuments(request: AnalyticsDocumentRequest): Promise<string>;
    /**
     * Get analytics document request status
     */
    getAnalyticsDocumentStatus(requestId: string): Promise<{
        id: string;
        status: AnalyticsDocumentStatus;
        documentType: AnalyticsDocumentType;
        recipientType: AnalyticsRecipientType;
        recipientEmail: string | undefined;
        createdAt: Date;
        updatedAt: Date;
        expiresAt: Date;
        errorMessage: string | undefined;
        processedAt: Date | undefined;
        documentCount: number;
        totalSize: number;
    }>;
    /**
     * Process analytics documents in background (called by SQS handler)
     */
    processAnalyticsDocuments(requestId: string): Promise<void>;
    /**
     * Process documents by type - Generate both PDF and Excel files
     */
    private processDocumentsByType;
    /**
     * Fetch invoice data for the specified period
     */
    private fetchInvoiceData;
    /**
     * Fetch receipt data for the specified period
     */
    private fetchReceiptData;
    /**
     * Fetch credit note data for the specified period
     */
    private fetchCreditNoteData;
    /**
     * Convert invoice data to Excel format
     */
    private convertInvoicesToExcelFormat;
    /**
     * Upload Excel file to S3 with proper Promise handling
     */
    private uploadExcelToS3;
    /**
     * Convert receipt data to Excel format
     */
    private convertReceiptsToExcelFormat;
    /**
     * Convert credit note data to Excel format
     */
    private convertCreditNotesToExcelFormat;
    /**
     * Generate Excel report from data
     */
    private generateExcelReport;
    /**
     * Generate PDF report by stitching individual document PDFs
     */
    private generatePdfReport;
    /**
     * Generate PDF for a single invoice using professional template
     */
    private generateInvoicePdf;
    /**
     * Generate PDF for a single receipt using professional template
     */
    private generateReceiptPdf;
    /**
     * Generate PDF for a single credit note using professional template
     */
    private generateCreditNotePdf;
    /**
     * Prepare invoice data for professional template
     * Uses the same pattern as send-document service
     */
    private prepareInvoiceData;
    /**
     * Prepare receipt data for professional template
     */
    private prepareReceiptData;
    /**
     * Prepare credit note data for professional template
     * Credit notes are actually invoice entities with invoiceType === Refund
     */
    private prepareCreditNoteData;
    /**
     * Get formatted clinic address (same pattern as send-document service)
     */
    private getClinicAddress;
    /**
     * Send analytics email with PDF and Excel attachments
     */
    private sendAnalyticsEmail;
    /**
     * Cleanup expired analytics document requests and their associated S3 files
     * This method is called by the cron job daily
     */
    cleanupExpiredAnalyticsDocuments(): Promise<{
        deletedRequests: number;
        deletedFiles: number;
        errors: string[];
    }>;
    /**
     * Get analytics document cleanup metrics for monitoring
     */
    getCleanupMetrics(): Promise<{
        totalRequests: number;
        expiredRequests: number;
        requestsByStatus: Record<string, number>;
        oldestRequest: Date | null;
        newestRequest: Date | null;
    }>;
    /**
     * Force cleanup of specific analytics document request
     * Useful for manual cleanup or testing
     */
    forceCleanupRequest(requestId: string): Promise<boolean>;
}
