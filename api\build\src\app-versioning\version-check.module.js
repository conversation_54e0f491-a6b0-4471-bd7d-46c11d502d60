"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionCheckModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const config_2 = require("../config/config");
const version_check_controller_1 = require("./version-check.controller");
const version_check_service_1 = require("./version-check.service");
let VersionCheckModule = class VersionCheckModule {
};
exports.VersionCheckModule = VersionCheckModule;
exports.VersionCheckModule = VersionCheckModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                load: [config_2.default]
            })
        ],
        providers: [version_check_service_1.VersionCheckService],
        controllers: [version_check_controller_1.VersionCheckController]
    })
], VersionCheckModule);
//# sourceMappingURL=version-check.module.js.map