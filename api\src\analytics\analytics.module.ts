import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { Patient } from '../patients/entities/patient.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { RoleModule } from '../roles/role.module';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { AnalyticsDocumentRequestEntity } from '../analytics-sharing/entities/analytics-document-request.entity';
import { AnalyticsDocumentService } from '../analytics-sharing/services/analytics-document.service';
import { AnalyticsMonitoringService } from '../analytics-sharing/services/analytics-monitoring.service';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { UsersModule } from '../users/users.module';
import { RedisModule } from '../utils/redis/redis.module';
@Module({
	imports: [
		TypeOrmModule.forFeature([
			InvoiceEntity,
			PaymentDetailsEntity,
			Patient,
			OwnerBrand,
			AppointmentEntity,
			ClinicEntity,
			AnalyticsDocumentRequestEntity
		]),
		RoleModule,
		forwardRef(() => UsersModule),
		RedisModule
	],
	controllers: [AnalyticsController],
	providers: [
		AnalyticsService,
		AnalyticsDocumentService,
		AnalyticsMonitoringService,
		SqsService,
		WinstonLogger,
		S3Service
	],
	exports: [AnalyticsService]
})
export class AnalyticsModule {}
