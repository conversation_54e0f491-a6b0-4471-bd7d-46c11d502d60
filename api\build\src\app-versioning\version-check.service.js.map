{"version": 3, "file": "version-check.service.js", "sourceRoot": "", "sources": ["../../../src/app-versioning/version-check.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAGrC,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC/B,aAAa,CAAC,WAAmB,EAAE,MAAW;;QAC7C,6DAA6D;QAC7D,MAAM,UAAU,GAAG,gBAAgB,CAAC;QACpC,MAAM,kBAAkB,GAAG,MAAM,CAAC,cAAc,CAAC;QACjD,MAAM,qBAAqB,GAAG,MAAM,CAAC,iBAAiB,CAAC;QAEvD,IAAI,WAAW,KAAK,kBAAkB,IAAI,qBAAqB,EAAE,CAAC;YACjE,IAAI,WAAW,KAAK,qBAAqB,EAAE,CAAC;gBAC3C,OAAO;oBACN,YAAY,EAAE,KAAK;oBACnB,aAAa,EAAE,qBAAqB;oBACpC,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,cAAc,KAAI,EAAE,CAAC,CAAC,GAAG,CACxD,CAAC,WAAgB,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CACtC;iBACD,CAAC;YACH,CAAC;YACD,OAAO;gBACN,YAAY,EAAE,IAAI;gBAClB,aAAa,EAAE,qBAAqB;gBACpC,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,cAAc,KAAI,EAAE,CAAC,CAAC,GAAG,CACxD,CAAC,WAAgB,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CACtC;aACD,CAAC;QACH,CAAC;QAED,IAAI,qBAAqB,EAAE,CAAC;YAC3B,MAAM,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,GACjE,qBAAqB;iBACnB,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,GAAG,WAAW;iBAC5D,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAEpC,IACC,kBAAkB,GAAG,YAAY;gBACjC,CAAC,kBAAkB,KAAK,YAAY;oBACnC,kBAAkB,GAAG,YAAY,CAAC;gBACnC,CAAC,kBAAkB,KAAK,YAAY;oBACnC,kBAAkB,KAAK,YAAY;oBACnC,kBAAkB,GAAG,YAAY,CAAC,EAClC,CAAC;gBACF,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CACrC,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,cAAc,mCAAI,EAAE,CAC5B;oBACA,6DAA6D;qBAC5D,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,WAAW,CAAM,EAAE,EAAE;oBACvC,MAAM,CACL,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,CACnB,GAAG,WAAW,CAAC,OAAO;yBACrB,KAAK,CAAC,GAAG,CAAC;yBACV,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;oBACpC,OAAO,CACN,mBAAmB,GAAG,YAAY;wBAClC,CAAC,mBAAmB,KAAK,YAAY;4BACpC,mBAAmB,GAAG,YAAY,CAAC;wBACpC,CAAC,mBAAmB,KAAK,YAAY;4BACpC,mBAAmB,KAAK,YAAY;4BACpC,mBAAmB,GAAG,YAAY,CAAC,CACpC,CAAC;gBACH,CAAC,CAAC;oBACF,6DAA6D;qBAC5D,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,WAAW,CAAM,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAEzD,OAAO;oBACN,YAAY,EAAE,IAAI;oBAClB,aAAa,EAAE,qBAAqB;oBACpC,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,eAAe;iBACzB,CAAC;YACH,CAAC;QACF,CAAC;QAED,OAAO;YACN,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,qBAAqB;YACpC,WAAW,EAAE,KAAK;YAClB,QAAQ,EAAE,EAAE;SACZ,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,WAAmB,EAAE,MAAW;QAChD,IAAI,CAAC;YACJ,6DAA6D;YAC7D,MAAM,UAAU,GAAG,gBAAgB,CAAC;YACpC,MAAM,qBAAqB,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACvD,MAAM,kBAAkB,GAAG,MAAM,CAAC,cAAc,CAAC;YAEjD,IAAI,qBAAqB,EAAE,CAAC;gBAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBACvD,OAAO,MAAM,CAAC;YACf,CAAC;iBAAM,CAAC;gBACP,OAAO;oBACN,YAAY,EAAE,KAAK;oBACnB,aAAa,EAAE,qBAAqB;wBACnC,CAAC,CAAC,qBAAqB;wBACvB,CAAC,CAAC,kBAAkB;oBACrB,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,EAAE;iBACZ,CAAC;YACH,CAAC;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,sDAAsD;YACtD,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;CACD,CAAA;AA/GY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;GACA,mBAAmB,CA+G/B"}