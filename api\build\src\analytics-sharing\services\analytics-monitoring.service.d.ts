import { Repository } from 'typeorm';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { AnalyticsDocumentRequestEntity, AnalyticsDocumentStatus } from '../entities/analytics-document-request.entity';
import { RedisService } from '../../utils/redis/redis.service';
export interface AnalyticsMetrics {
    totalRequests: number;
    requestsByStatus: Record<AnalyticsDocumentStatus, number>;
    averageProcessingTime: number;
    successRate: number;
    errorRate: number;
    requestsLast24Hours: number;
    requestsLast7Days: number;
    averageFileSize: number;
    peakHours: {
        hour: number;
        count: number;
    }[];
    topDocumentTypes: {
        type: string;
        count: number;
    }[];
}
export interface PerformanceMetrics {
    processingTimeMs: number;
    documentCount: number;
    totalSizeBytes: number;
    pdfGenerationTimeMs?: number;
    excelGenerationTimeMs?: number;
    s3UploadTimeMs?: number;
    emailSendTimeMs?: number;
}
export declare class AnalyticsMonitoringService {
    private readonly logger;
    private readonly analyticsDocumentRequestRepository;
    private readonly redisService;
    private readonly RATE_LIMIT_KEY_PREFIX;
    private readonly METRICS_CACHE_KEY;
    private readonly CACHE_TTL;
    constructor(logger: WinstonLogger, analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>, redisService: RedisService);
    /**
     * Record performance metrics for analytics document processing
     */
    recordPerformanceMetrics(requestId: string, metrics: PerformanceMetrics): Promise<void>;
    /**
     * Get comprehensive analytics metrics
     */
    getAnalyticsMetrics(): Promise<AnalyticsMetrics>;
    /**
     * Check rate limiting for analytics document requests
     */
    checkRateLimit(userId: string, clinicId: string): Promise<{
        allowed: boolean;
        remaining: number;
        resetTime: Date;
    }>;
    /**
     * Get performance metrics for a specific request
     */
    getRequestPerformanceMetrics(requestId: string): Promise<PerformanceMetrics | null>;
    /**
     * Get system health metrics for analytics processing
     */
    getSystemHealthMetrics(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        metrics: {
            activeRequests: number;
            failureRate: number;
            averageProcessingTime: number;
            queueDepth: number;
        };
    }>;
    private incrementRateLimit;
    private calculateMetrics;
}
