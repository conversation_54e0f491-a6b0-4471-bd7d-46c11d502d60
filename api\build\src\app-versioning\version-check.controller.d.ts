import { ConfigService } from '@nestjs/config';
import { VersionCheckService } from './version-check.service';
export declare class VersionCheckController {
    private readonly versionCheckService;
    private readonly configService;
    constructor(versionCheckService: VersionCheckService, configService: ConfigService);
    getAppVersion(body: any): Promise<{
        success: boolean;
        message: string;
        data: any;
    }>;
}
