{"version": 3, "file": "sqs-queue.config.js", "sourceRoot": "", "sources": ["../../../../../src/utils/aws/sqs/sqs-queue.config.ts"], "names": [], "mappings": ";;;AA2DA,oDAoBC;AA9ED,sFAA0E;AAC1E,8FAAwF;AACxF,4FAAsF;AACtF,wGAAkG;AAClG,kHAA4G;AAC5G,wGAAkG;AAErF,QAAA,MAAM,GAAgC;IAClD,eAAe,EAAE;QAChB,IAAI,EAAE,iBAAiB;QACvB,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,8CAAiB;QAC1B,eAAe,EAAE,CAAC;QAClB,sBAAsB,EAAE,KAAK;QAC7B,OAAO,EAAE,uBAAuB;KAChC;IACD,mBAAmB,EAAE;QACpB,IAAI,EAAE,qBAAqB;QAC3B,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,4DAA2B;QACpC,eAAe,EAAE,CAAC;QAClB,sBAAsB,EAAE,KAAK;QAC7B,OAAO,EAAE,uBAAuB;KAChC;IACD,kBAAkB,EAAE;QACnB,IAAI,EAAE,oBAAoB;QAC1B,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,0DAA0B;QACnC,eAAe,EAAE,CAAC;QAClB,sBAAsB,EAAE,KAAK;QAC7B,OAAO,EAAE,uBAAuB;KAChC;IACD,wBAAwB,EAAE;QACzB,IAAI,EAAE,0BAA0B;QAChC,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,sEAAgC;QACzC,eAAe,EAAE,CAAC;QAClB,sBAAsB,EAAE,KAAK;QAC7B,OAAO,EAAE,uBAAuB;KAChC;IACD,6BAA6B,EAAE;QAC9B,IAAI,EAAE,+BAA+B;QACrC,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,gFAAqC;QAC9C,eAAe,EAAE,CAAC,EAAE,sCAAsC;QAC1D,sBAAsB,EAAE,KAAK;QAC7B,OAAO,EAAE,uBAAuB;KAChC;IACD,wBAAwB,EAAE;QACzB,IAAI,EAAE,0BAA0B;QAChC,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,sEAAgC;QACzC,eAAe,EAAE,CAAC,EAAE,2CAA2C;QAC/D,sBAAsB,EAAE,KAAK;QAC7B,OAAO,EAAE,uBAAuB;KAChC;CACD,CAAC;AAEF,SAAgB,oBAAoB,CAAC,GAAW;IAC/C,wFAAwF;IACxF,MAAM,MAAM,GACX,GAAG,KAAK,aAAa;QACpB,CAAC,CAAC,KAAK;QACP,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,iBAAiB,GAAgC,EAAE,CAAC;IAE1D,MAAM,CAAC,OAAO,CAAC,cAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC/C,iBAAiB,CAAC,GAAG,CAAC,GAAG;YACxB,IAAI,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE;YAC9B,YAAY,EAAE,KAAK,CAAC,YAAY;YAChC,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,eAAe,EAAE,KAAK,CAAC,eAAe;YACtC,sBAAsB,EAAE,KAAK,CAAC,sBAAsB;YACpD,OAAO,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC,OAAO,EAAE;SACpC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,iBAAiB,CAAC;AAC1B,CAAC"}