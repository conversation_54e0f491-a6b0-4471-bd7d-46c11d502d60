import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { AnalyticsDocumentRequestEntity, AnalyticsDocumentStatus } from '../entities/analytics-document-request.entity';
import { RedisService } from '../../utils/redis/redis.service';

export interface AnalyticsMetrics {
	totalRequests: number;
	requestsByStatus: Record<AnalyticsDocumentStatus, number>;
	averageProcessingTime: number;
	successRate: number;
	errorRate: number;
	requestsLast24Hours: number;
	requestsLast7Days: number;
	averageFileSize: number;
	peakHours: { hour: number; count: number }[];
	topDocumentTypes: { type: string; count: number }[];
}

export interface PerformanceMetrics {
	processingTimeMs: number;
	documentCount: number;
	totalSizeBytes: number;
	pdfGenerationTimeMs?: number;
	excelGenerationTimeMs?: number;
	s3UploadTimeMs?: number;
	emailSendTimeMs?: number;
}

@Injectable()
export class AnalyticsMonitoringService {
	private readonly RATE_LIMIT_KEY_PREFIX = 'analytics_rate_limit';
	private readonly METRICS_CACHE_KEY = 'analytics_metrics_cache';
	private readonly CACHE_TTL = 300; // 5 minutes

	constructor(
		private readonly logger: WinstonLogger,
		@InjectRepository(AnalyticsDocumentRequestEntity)
		private readonly analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>,
		private readonly redisService: RedisService
	) {}

	/**
	 * Record performance metrics for analytics document processing
	 */
	async recordPerformanceMetrics(requestId: string, metrics: PerformanceMetrics): Promise<void> {
		try {
			const metricsKey = `analytics_performance:${requestId}`;
			const metricsData = {
				...metrics,
				timestamp: new Date().toISOString(),
				requestId
			};

			// Store metrics in Redis with 7-day expiration
			await this.redisService.getClient().set(metricsKey, JSON.stringify(metricsData), 'EX', 7 * 24 * 60 * 60);

			// Log performance metrics
			this.logger.log('Analytics performance metrics recorded', {
				requestId,
				processingTimeMs: metrics.processingTimeMs,
				documentCount: metrics.documentCount,
				totalSizeBytes: metrics.totalSizeBytes,
				throughputDocsPerSecond: metrics.documentCount / (metrics.processingTimeMs / 1000)
			});

		} catch (error) {
			this.logger.error('Failed to record performance metrics', {
				requestId,
				error: error instanceof Error ? error.message : String(error)
			});
		}
	}

	/**
	 * Get comprehensive analytics metrics
	 */
	async getAnalyticsMetrics(): Promise<AnalyticsMetrics> {
		try {
			// Try to get cached metrics first
			const cachedMetrics = await this.redisService.get(this.METRICS_CACHE_KEY);
			if (cachedMetrics) {
				return JSON.parse(cachedMetrics);
			}

			// Calculate metrics from database
			const metrics = await this.calculateMetrics();

			// Cache the metrics
			await this.redisService.getClient().set(this.METRICS_CACHE_KEY, JSON.stringify(metrics), 'EX', this.CACHE_TTL);

			return metrics;

		} catch (error) {
			this.logger.error('Failed to get analytics metrics', {
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}
	}

	/**
	 * Check rate limiting for analytics document requests
	 */
	async checkRateLimit(userId: string, clinicId: string): Promise<{
		allowed: boolean;
		remaining: number;
		resetTime: Date;
	}> {
		try {
			const userKey = `${this.RATE_LIMIT_KEY_PREFIX}:user:${userId}`;
			const clinicKey = `${this.RATE_LIMIT_KEY_PREFIX}:clinic:${clinicId}`;

			// Rate limits: 10 requests per hour per user, 50 requests per hour per clinic
			const userLimit = 10;
			const clinicLimit = 50;
			const windowSeconds = 3600; // 1 hour

			// Check user rate limit
			const userCount = await this.incrementRateLimit(userKey, windowSeconds);
			const userAllowed = userCount <= userLimit;

			// Check clinic rate limit
			const clinicCount = await this.incrementRateLimit(clinicKey, windowSeconds);
			const clinicAllowed = clinicCount <= clinicLimit;

			const allowed = userAllowed && clinicAllowed;
			const remaining = Math.min(
				Math.max(0, userLimit - userCount),
				Math.max(0, clinicLimit - clinicCount)
			);

			const resetTime = new Date(Date.now() + windowSeconds * 1000);

			if (!allowed) {
				this.logger.warn('Analytics rate limit exceeded', {
					userId,
					clinicId,
					userCount,
					clinicCount,
					userLimit,
					clinicLimit
				});
			}

			return { allowed, remaining, resetTime };

		} catch (error) {
			this.logger.error('Failed to check rate limit', {
				userId,
				clinicId,
				error: error instanceof Error ? error.message : String(error)
			});
			// Allow request if rate limiting fails
			return { allowed: true, remaining: 0, resetTime: new Date() };
		}
	}

	/**
	 * Get performance metrics for a specific request
	 */
	async getRequestPerformanceMetrics(requestId: string): Promise<PerformanceMetrics | null> {
		try {
			const metricsKey = `analytics_performance:${requestId}`;
			const metricsData = await this.redisService.get(metricsKey);

			if (!metricsData) {
				return null;
			}

			const metrics = JSON.parse(metricsData);
			delete metrics.timestamp;
			delete metrics.requestId;

			return metrics;

		} catch (error) {
			this.logger.error('Failed to get request performance metrics', {
				requestId,
				error: error instanceof Error ? error.message : String(error)
			});
			return null;
		}
	}

	/**
	 * Get system health metrics for analytics processing
	 */
	async getSystemHealthMetrics(): Promise<{
		status: 'healthy' | 'degraded' | 'unhealthy';
		metrics: {
			activeRequests: number;
			failureRate: number;
			averageProcessingTime: number;
			queueDepth: number;
		};
	}> {
		try {
			const now = new Date();
			const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

			// Get recent requests
			const recentRequests = await this.analyticsDocumentRequestRepository.find({
				where: {
					createdAt: oneHourAgo
				},
				select: ['status', 'createdAt', 'processedAt']
			});

			const activeRequests = recentRequests.filter(r => 
				r.status === AnalyticsDocumentStatus.PENDING || 
				r.status === AnalyticsDocumentStatus.PROCESSING
			).length;

			const completedRequests = recentRequests.filter(r => 
				r.status === AnalyticsDocumentStatus.COMPLETED ||
				r.status === AnalyticsDocumentStatus.FAILED
			);

			const failedRequests = recentRequests.filter(r => 
				r.status === AnalyticsDocumentStatus.FAILED
			).length;

			const failureRate = completedRequests.length > 0 ? 
				(failedRequests / completedRequests.length) * 100 : 0;

			// Calculate average processing time
			const processedRequests = completedRequests.filter(r => r.processedAt);
			const averageProcessingTime = processedRequests.length > 0 ?
				processedRequests.reduce((sum, r) => {
					const processingTime = r.processedAt!.getTime() - r.createdAt.getTime();
					return sum + processingTime;
				}, 0) / processedRequests.length : 0;

			// Determine system status
			let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
			if (failureRate > 20 || averageProcessingTime > 300000) { // 5 minutes
				status = 'unhealthy';
			} else if (failureRate > 10 || averageProcessingTime > 180000) { // 3 minutes
				status = 'degraded';
			}

			return {
				status,
				metrics: {
					activeRequests,
					failureRate,
					averageProcessingTime,
					queueDepth: activeRequests // Simplified queue depth
				}
			};

		} catch (error) {
			this.logger.error('Failed to get system health metrics', {
				error: error instanceof Error ? error.message : String(error)
			});
			return {
				status: 'unhealthy',
				metrics: {
					activeRequests: 0,
					failureRate: 100,
					averageProcessingTime: 0,
					queueDepth: 0
				}
			};
		}
	}

	private async incrementRateLimit(key: string, windowSeconds: number): Promise<number> {
		const client = this.redisService.getClient();
		
		// Use Redis pipeline for atomic operations
		const pipeline = client.pipeline();
		pipeline.incr(key);
		pipeline.expire(key, windowSeconds);
		
		const results = await pipeline.exec();
		return results?.[0]?.[1] as number || 0;
	}

	private async calculateMetrics(): Promise<AnalyticsMetrics> {
		const now = new Date();
		const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
		const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

		// Get all requests for comprehensive metrics
		const allRequests = await this.analyticsDocumentRequestRepository.find({
			select: ['status', 'documentType', 'createdAt', 'processedAt', 'totalSize']
		});

		const totalRequests = allRequests.length;

		// Requests by status
		const requestsByStatus = allRequests.reduce((acc, request) => {
			acc[request.status] = (acc[request.status] || 0) + 1;
			return acc;
		}, {} as Record<AnalyticsDocumentStatus, number>);

		// Processing time calculation
		const processedRequests = allRequests.filter(r => r.processedAt);
		const averageProcessingTime = processedRequests.length > 0 ?
			processedRequests.reduce((sum, r) => {
				const processingTime = r.processedAt!.getTime() - r.createdAt.getTime();
				return sum + processingTime;
			}, 0) / processedRequests.length : 0;

		// Success and error rates
		const completedRequests = requestsByStatus[AnalyticsDocumentStatus.COMPLETED] || 0;
		const failedRequests = requestsByStatus[AnalyticsDocumentStatus.FAILED] || 0;
		const totalCompleted = completedRequests + failedRequests;
		const successRate = totalCompleted > 0 ? (completedRequests / totalCompleted) * 100 : 0;
		const errorRate = totalCompleted > 0 ? (failedRequests / totalCompleted) * 100 : 0;

		// Recent requests
		const requestsLast24Hours = allRequests.filter(r => r.createdAt >= oneDayAgo).length;
		const requestsLast7Days = allRequests.filter(r => r.createdAt >= sevenDaysAgo).length;

		// Average file size
		const requestsWithSize = allRequests.filter(r => r.totalSize > 0);
		const averageFileSize = requestsWithSize.length > 0 ?
			requestsWithSize.reduce((sum, r) => sum + r.totalSize, 0) / requestsWithSize.length : 0;

		// Peak hours analysis
		const hourCounts = allRequests.reduce((acc, request) => {
			const hour = request.createdAt.getHours();
			acc[hour] = (acc[hour] || 0) + 1;
			return acc;
		}, {} as Record<number, number>);

		const peakHours = Object.entries(hourCounts)
			.map(([hour, count]) => ({ hour: parseInt(hour), count }))
			.sort((a, b) => b.count - a.count)
			.slice(0, 5);

		// Top document types
		const typeCounts = allRequests.reduce((acc, request) => {
			acc[request.documentType] = (acc[request.documentType] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		const topDocumentTypes = Object.entries(typeCounts)
			.map(([type, count]) => ({ type, count }))
			.sort((a, b) => b.count - a.count);

		return {
			totalRequests,
			requestsByStatus,
			averageProcessingTime,
			successRate,
			errorRate,
			requestsLast24Hours,
			requestsLast7Days,
			averageFileSize,
			peakHours,
			topDocumentTypes
		};
	}
}
