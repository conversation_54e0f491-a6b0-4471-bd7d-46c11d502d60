{"version": 3, "file": "analytics-monitoring.service.js", "sourceRoot": "", "sources": ["../../../../src/analytics-sharing/services/analytics-monitoring.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,sFAA0E;AAC1E,qGAAwH;AACxH,mEAA+D;AA0BxD,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAKtC,YACkB,MAAqB,EAEtC,kCAA+F,EAC9E,YAA0B;QAH1B,WAAM,GAAN,MAAM,CAAe;QAErB,uCAAkC,GAAlC,kCAAkC,CAA4C;QAC9E,iBAAY,GAAZ,YAAY,CAAc;QAR3B,0BAAqB,GAAG,sBAAsB,CAAC;QAC/C,sBAAiB,GAAG,yBAAyB,CAAC;QAC9C,cAAS,GAAG,GAAG,CAAC,CAAC,YAAY;IAO3C,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,SAAiB,EAAE,OAA2B;QAC5E,IAAI,CAAC;YACJ,MAAM,UAAU,GAAG,yBAAyB,SAAS,EAAE,CAAC;YACxD,MAAM,WAAW,GAAG;gBACnB,GAAG,OAAO;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS;aACT,CAAC;YAEF,+CAA+C;YAC/C,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEzG,0BAA0B;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,EAAE;gBACzD,SAAS;gBACT,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,uBAAuB,EAAE,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;aAClF,CAAC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACzD,SAAS;gBACT,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC7D,CAAC,CAAC;QACJ,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACxB,IAAI,CAAC;YACJ,kCAAkC;YAClC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC1E,IAAI,aAAa,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAClC,CAAC;YAED,kCAAkC;YAClC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE9C,oBAAoB;YACpB,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/G,OAAO,OAAO,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACpD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC7D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,QAAgB;QAKpD,IAAI,CAAC;YACJ,MAAM,OAAO,GAAG,GAAG,IAAI,CAAC,qBAAqB,SAAS,MAAM,EAAE,CAAC;YAC/D,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,qBAAqB,WAAW,QAAQ,EAAE,CAAC;YAErE,8EAA8E;YAC9E,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,MAAM,WAAW,GAAG,EAAE,CAAC;YACvB,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,SAAS;YAErC,wBAAwB;YACxB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACxE,MAAM,WAAW,GAAG,SAAS,IAAI,SAAS,CAAC;YAE3C,0BAA0B;YAC1B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAC5E,MAAM,aAAa,GAAG,WAAW,IAAI,WAAW,CAAC;YAEjD,MAAM,OAAO,GAAG,WAAW,IAAI,aAAa,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CACzB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,EAClC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,WAAW,CAAC,CACtC,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,GAAG,IAAI,CAAC,CAAC;YAE9D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;oBACjD,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,WAAW;iBACX,CAAC,CAAC;YACJ,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;QAE1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC/C,MAAM;gBACN,QAAQ;gBACR,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC7D,CAAC,CAAC;YACH,uCAAuC;YACvC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;QAC/D,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,4BAA4B,CAAC,SAAiB;QACnD,IAAI,CAAC;YACJ,MAAM,UAAU,GAAG,yBAAyB,SAAS,EAAE,CAAC;YACxD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAE5D,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC;YACb,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACxC,OAAO,OAAO,CAAC,SAAS,CAAC;YACzB,OAAO,OAAO,CAAC,SAAS,CAAC;YAEzB,OAAO,OAAO,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;gBAC9D,SAAS;gBACT,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC7D,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB;QAS3B,IAAI,CAAC;YACJ,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAE5D,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC;gBACzE,KAAK,EAAE;oBACN,SAAS,EAAE,UAAU;iBACrB;gBACD,MAAM,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC;aAC9C,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChD,CAAC,CAAC,MAAM,KAAK,2DAAuB,CAAC,OAAO;gBAC5C,CAAC,CAAC,MAAM,KAAK,2DAAuB,CAAC,UAAU,CAC/C,CAAC,MAAM,CAAC;YAET,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACnD,CAAC,CAAC,MAAM,KAAK,2DAAuB,CAAC,SAAS;gBAC9C,CAAC,CAAC,MAAM,KAAK,2DAAuB,CAAC,MAAM,CAC3C,CAAC;YAEF,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChD,CAAC,CAAC,MAAM,KAAK,2DAAuB,CAAC,MAAM,CAC3C,CAAC,MAAM,CAAC;YAET,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjD,CAAC,cAAc,GAAG,iBAAiB,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEvD,oCAAoC;YACpC,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;YACvE,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3D,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;oBACnC,MAAM,cAAc,GAAG,CAAC,CAAC,WAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACxE,OAAO,GAAG,GAAG,cAAc,CAAC;gBAC7B,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtC,0BAA0B;YAC1B,IAAI,MAAM,GAAyC,SAAS,CAAC;YAC7D,IAAI,WAAW,GAAG,EAAE,IAAI,qBAAqB,GAAG,MAAM,EAAE,CAAC,CAAC,YAAY;gBACrE,MAAM,GAAG,WAAW,CAAC;YACtB,CAAC;iBAAM,IAAI,WAAW,GAAG,EAAE,IAAI,qBAAqB,GAAG,MAAM,EAAE,CAAC,CAAC,YAAY;gBAC5E,MAAM,GAAG,UAAU,CAAC;YACrB,CAAC;YAED,OAAO;gBACN,MAAM;gBACN,OAAO,EAAE;oBACR,cAAc;oBACd,WAAW;oBACX,qBAAqB;oBACrB,UAAU,EAAE,cAAc,CAAC,yBAAyB;iBACpD;aACD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACxD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC7D,CAAC,CAAC;YACH,OAAO;gBACN,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE;oBACR,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,GAAG;oBAChB,qBAAqB,EAAE,CAAC;oBACxB,UAAU,EAAE,CAAC;iBACb;aACD,CAAC;QACH,CAAC;IACF,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,GAAW,EAAE,aAAqB;;QAClE,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;QAE7C,2CAA2C;QAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACnC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QAEpC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACtC,OAAO,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,CAAC,CAAC,0CAAG,CAAC,CAAW,KAAI,CAAC,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC7B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAChE,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEvE,6CAA6C;QAC7C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC;YACtE,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;SAC3E,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC;QAEzC,qBAAqB;QACrB,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YAC5D,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,OAAO,GAAG,CAAC;QACZ,CAAC,EAAE,EAA6C,CAAC,CAAC;QAElD,8BAA8B;QAC9B,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3D,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,cAAc,GAAG,CAAC,CAAC,WAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACxE,OAAO,GAAG,GAAG,cAAc,CAAC;YAC7B,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,2DAAuB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnF,MAAM,cAAc,GAAG,gBAAgB,CAAC,2DAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC7E,MAAM,cAAc,GAAG,iBAAiB,GAAG,cAAc,CAAC;QAC1D,MAAM,WAAW,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxF,MAAM,SAAS,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnF,kBAAkB;QAClB,MAAM,mBAAmB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC;QACrF,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,YAAY,CAAC,CAAC,MAAM,CAAC;QAEtF,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QAClE,MAAM,eAAe,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACpD,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzF,sBAAsB;QACtB,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACtD,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC1C,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC;QACZ,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;aAC1C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;aACzD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEd,qBAAqB;QACrB,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACtD,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjE,OAAO,GAAG,CAAC;QACZ,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;aACjD,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;aACzC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAEpC,OAAO;YACN,aAAa;YACb,gBAAgB;YAChB,qBAAqB;YACrB,WAAW;YACX,SAAS;YACT,mBAAmB;YACnB,iBAAiB;YACjB,eAAe;YACf,SAAS;YACT,gBAAgB;SAChB,CAAC;IACH,CAAC;CACD,CAAA;AAvUY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAQV,WAAA,IAAA,0BAAgB,EAAC,kEAA8B,CAAC,CAAA;qCADxB,sCAAa;QAEe,oBAAU;QAChC,4BAAY;GAThC,0BAA0B,CAuUtC"}