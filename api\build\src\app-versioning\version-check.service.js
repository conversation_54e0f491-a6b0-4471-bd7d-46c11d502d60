"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionCheckService = void 0;
const common_1 = require("@nestjs/common");
let VersionCheckService = class VersionCheckService {
    verifyVersion(versionName, config) {
        var _a;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const versionKey = 'webAppVersions';
        const minVersionOnServer = config.minVersionName;
        const latestVersionOnServer = config.latestVersionName;
        if (versionName === minVersionOnServer && latestVersionOnServer) {
            if (versionName === latestVersionOnServer) {
                return {
                    notifyUpdate: false,
                    latestVersion: latestVersionOnServer,
                    forceUpdate: false,
                    features: Object.values((config === null || config === void 0 ? void 0 : config.featuresUpdate) || {}).map((featureInfo) => featureInfo.path)
                };
            }
            return {
                notifyUpdate: true,
                latestVersion: latestVersionOnServer,
                forceUpdate: true,
                features: Object.values((config === null || config === void 0 ? void 0 : config.featuresUpdate) || {}).map((featureInfo) => featureInfo.path)
            };
        }
        if (latestVersionOnServer) {
            const [serverMajorVersion, serverMinorVersion, serverPatchVersion] = latestVersionOnServer
                .split('.')
                .map((val) => Number(val));
            const [majorVersion, minorVersion, patchVersion] = versionName
                .split('.')
                .map((val) => Number(val));
            if (serverMajorVersion > majorVersion ||
                (serverMajorVersion === majorVersion &&
                    serverMinorVersion > minorVersion) ||
                (serverMajorVersion === majorVersion &&
                    serverMinorVersion === minorVersion &&
                    serverPatchVersion > patchVersion)) {
                const featureVersions = Object.entries((_a = config === null || config === void 0 ? void 0 : config.featuresUpdate) !== null && _a !== void 0 ? _a : {})
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    .filter(([feature, featureInfo]) => {
                    const [featureMajorVersion, featureMinorVersion, featurePatchVersion] = featureInfo.version
                        .split('.')
                        .map((val) => Number(val));
                    return (featureMajorVersion > majorVersion ||
                        (featureMajorVersion === majorVersion &&
                            featureMinorVersion > minorVersion) ||
                        (featureMajorVersion === majorVersion &&
                            featureMinorVersion === minorVersion &&
                            featurePatchVersion > patchVersion));
                })
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    .map(([feature, featureInfo]) => featureInfo.path);
                return {
                    notifyUpdate: true,
                    latestVersion: latestVersionOnServer,
                    forceUpdate: false,
                    features: featureVersions
                };
            }
        }
        return {
            notifyUpdate: false,
            latestVersion: latestVersionOnServer,
            forceUpdate: false,
            features: []
        };
    }
    updateAppVersion(versionName, config) {
        try {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const versionKey = 'webAppVersions';
            const latestVersionOnServer = config.latestVersionName;
            const minVersionOnServer = config.minVersionName;
            if (latestVersionOnServer) {
                const result = this.verifyVersion(versionName, config);
                return result;
            }
            else {
                return {
                    notifyUpdate: false,
                    latestVersion: latestVersionOnServer
                        ? latestVersionOnServer
                        : minVersionOnServer,
                    forceUpdate: false,
                    features: []
                };
            }
        }
        catch (error) {
            // console.error('Error in updateAppVersion:', error);
            throw error;
        }
    }
};
exports.VersionCheckService = VersionCheckService;
exports.VersionCheckService = VersionCheckService = __decorate([
    (0, common_1.Injectable)()
], VersionCheckService);
//# sourceMappingURL=version-check.service.js.map