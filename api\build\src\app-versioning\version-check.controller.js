"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionCheckController = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const version_check_service_1 = require("./version-check.service");
let VersionCheckController = class VersionCheckController {
    constructor(versionCheckService, configService) {
        this.versionCheckService = versionCheckService;
        this.configService = configService;
    }
    async getAppVersion(body) {
        try {
            const { versionName } = body;
            const config = this.configService.get('webAppVersions');
            const response = await this.versionCheckService.updateAppVersion(versionName, config);
            return {
                success: true,
                message: 'Successfully got app version!',
                data: response
            };
        }
        catch (error) {
            throw new common_1.HttpException('Internal server error', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.VersionCheckController = VersionCheckController;
__decorate([
    (0, common_1.Post)('app-version'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VersionCheckController.prototype, "getAppVersion", null);
exports.VersionCheckController = VersionCheckController = __decorate([
    (0, common_1.Controller)('web'),
    __metadata("design:paramtypes", [version_check_service_1.VersionCheckService,
        config_1.ConfigService])
], VersionCheckController);
//# sourceMappingURL=version-check.controller.js.map