{"version": 3, "file": "analytics.controller.js", "sourceRoot": "", "sources": ["../../../src/analytics/analytics.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAWwB;AACxB,6CAKyB;AAEzB,kEAA6D;AAC7D,4DAAwD;AACxD,8DAAiD;AACjD,kDAA0C;AAC1C,2DAAuD;AACvD,uDAW6B;AAC7B,0GAIgE;AAChE,yGAAoG;AACpG,6GAAwG;AACxG,mCAAgC;AAChC,uHAGyE;AACzE,iGAAmF;AAO5E,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC/B,YACkB,gBAAkC,EAClC,wBAAkD,EAClD,0BAAsD;QAFtD,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,+BAA0B,GAA1B,0BAA0B,CAA4B;IACrE,CAAC;IAME,AAAN,KAAK,CAAC,mBAAmB,CAExB,GAA2B;QAE3B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAC7D,CAAC;IAMK,AAAN,KAAK,CAAC,6BAA6B,CAElC,GAA2B;QAE3B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAMK,AAAN,KAAK,CAAC,wBAAwB,CAE7B,GAAgC;QAEhC,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;IAClE,CAAC;IAMK,AAAN,KAAK,CAAC,cAAc,CAEnB,GAA+B,EACxB,GAAa;QAEpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAE/D,GAAG,CAAC,GAAG,CAAC;YACP,cAAc,EACb,mEAAmE;YACpE,qBAAqB,EAAE,yBAAyB,GAAG,CAAC,IAAI,eAAe;YACvE,gBAAgB,EAAE,MAAM,CAAC,MAAM;YAC/B,eAAe,EAAE,UAAU;SAC3B,CAAC,CAAC;QAEH,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACjB,CAAC;IAMK,AAAN,KAAK,CAAC,gBAAgB,CAErB,GAAwB;QAExB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAUK,AAAN,KAAK,CAAC,UAAU,CACiC,GAAkB;QAElE,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAkBK,AAAN,KAAK,CAAC,uBAAuB,CACmB,GAA+B,EACvE,GAAoB;QAE3B,6BAA6B;QAC7B,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;QAE3B,+BAA+B;QAC/B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACrD,CAAC;QAED,4EAA4E;QAE5E,oCAAoC;QACpC,MAAM,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC;YAC3D,SAAS;YACT,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,mCAAmC;YACzD,MAAM;YACN,YAAY,EAAE,GAAG,CAAC,YAAY;YAC9B,aAAa,EAAE,GAAG,CAAC,aAAa;YAChC,cAAc,EAAE,GAAG,CAAC,cAAc;YAClC,cAAc,EAAE,GAAG,CAAC,cAAc;YAClC,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YAClC,OAAO,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;SAC9B,CAAC,CAAC;QAEH,OAAO;YACN,SAAS;YACT,MAAM,EAAE,2DAAuB,CAAC,OAAO;YACvC,OAAO,EAAE,oHAAoH;SAC7H,CAAC;IACH,CAAC;IAkBK,AAAN,KAAK,CAAC,0BAA0B,CACX,SAAiB;QAErC,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;IAClF,CAAC;IAOK,AAAN,KAAK,CAAC,mBAAmB;QACxB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,EAAE,CAAC;IACpE,CAAC;IAOK,AAAN,KAAK,CAAC,eAAe;QACpB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,sBAAsB,EAAE,CAAC;IACvE,CAAC;IAOK,AAAN,KAAK,CAAC,iBAAiB;QACtB,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,CAAC;IAChE,CAAC;CACD,CAAA;AA3LY,kDAAmB;AAWzB;IAJL,IAAA,YAAG,EAAC,oBAAoB,CAAC;IACzB,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IACnD,IAAA,oCAAW,EAAC,wBAAwB,CAAC;IAEpC,WAAA,IAAA,cAAK,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;;qCAC1C,sCAAsB;;8DAG3B;AAMK;IAJL,IAAA,YAAG,EAAC,+BAA+B,CAAC;IACpC,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC9D,IAAA,oCAAW,EAAC,mCAAmC,CAAC;IAE/C,WAAA,IAAA,cAAK,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;;qCAC1C,sCAAsB;;wEAG3B;AAMK;IAJL,IAAA,YAAG,EAAC,yBAAyB,CAAC;IAC9B,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACxD,IAAA,oCAAW,EAAC,6BAA6B,CAAC;IAEzC,WAAA,IAAA,cAAK,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;;qCAC1C,2CAA2B;;mEAGhC;AAMK;IAJL,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;IACtD,IAAA,oCAAW,EAAC,2BAA2B,CAAC;IAEvC,WAAA,IAAA,cAAK,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;IAE9C,WAAA,IAAA,YAAG,GAAE,CAAA;;qCADD,0CAA0B;;yDAc/B;AAMK;IAJL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IAC3D,IAAA,oCAAW,EAAC,oBAAoB,CAAC;IAEhC,WAAA,IAAA,cAAK,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;;qCAC1C,mCAAmB;;2DAGxB;AAUK;IARL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IAC3D,IAAA,qBAAW,EAAC;QACZ,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iCAAiC;KAC9C,CAAC;IACD,IAAA,oCAAW,EAAC,oBAAoB,CAAC;IAEhC,WAAA,IAAA,cAAK,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;;qCAAM,6BAAa;;qDAGlE;AAkBK;IAhBL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,EAAE,gBAAI,CAAC,MAAM,EAAE,gBAAI,CAAC,YAAY,CAAC;IACjD,IAAA,sBAAY,EAAC;QACb,OAAO,EAAE,qCAAqC;QAC9C,WAAW,EAAE,kGAAkG;KAC/G,CAAC;IACD,IAAA,qBAAW,EAAC;QACZ,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,kEAAkC;KACxC,CAAC;IACD,IAAA,qBAAW,EAAC;QACZ,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,4BAA4B;KACzC,CAAC;IACD,IAAA,oCAAW,EAAC,2BAA2B,CAAC;IAEvC,WAAA,IAAA,aAAI,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;IAC7C,WAAA,IAAA,YAAG,GAAE,CAAA;;qCAD8C,0DAA0B;;kEAiC9E;AAkBK;IAhBL,IAAA,YAAG,EAAC,mCAAmC,CAAC;IACxC,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,EAAE,gBAAI,CAAC,MAAM,EAAE,gBAAI,CAAC,YAAY,CAAC;IACjD,IAAA,sBAAY,EAAC;QACb,OAAO,EAAE,uCAAuC;QAChD,WAAW,EAAE,2DAA2D;KACxE,CAAC;IACD,IAAA,qBAAW,EAAC;QACZ,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mCAAmC;QAChD,IAAI,EAAE,0DAA0B;KAChC,CAAC;IACD,IAAA,qBAAW,EAAC;QACZ,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,sCAAsC;KACnD,CAAC;IACD,IAAA,oCAAW,EAAC,+BAA+B,CAAC;IAE3C,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;;;;qEAGnB;AAOK;IALL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,EAAE,gBAAI,CAAC,WAAW,CAAC;IACnC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IAC7D,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,0CAA0C,EAAE,CAAC;IACrF,IAAA,oCAAW,EAAC,uBAAuB,CAAC;;;;8DAGpC;AAOK;IALL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,uBAAK,EAAC,gBAAI,CAAC,KAAK,EAAE,gBAAI,CAAC,WAAW,CAAC;IACnC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAChE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,8CAA8C,EAAE,CAAC;IACzF,IAAA,oCAAW,EAAC,sBAAsB,CAAC;;;;0DAGnC;AAOK;IALL,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,uBAAK,EAAC,gBAAI,CAAC,WAAW,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC;IACnE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,wCAAwC,EAAE,CAAC;IACnF,IAAA,oCAAW,EAAC,+BAA+B,CAAC;;;;4DAG5C;8BA1LW,mBAAmB;IAJ/B,IAAA,iBAAO,EAAC,WAAW,CAAC;IACpB,IAAA,mBAAU,EAAC,WAAW,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,uBAAa,GAAE;qCAGqB,oCAAgB;QACR,qDAAwB;QACtB,yDAA0B;GAJ5D,mBAAmB,CA2L/B"}